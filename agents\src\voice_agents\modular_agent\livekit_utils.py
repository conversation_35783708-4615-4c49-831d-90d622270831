import os
import traceback
from livekit import api
from .config import logger, LIVEKIT_URL, GCS_BUCKET_NAME, GOOGLE_JSON_CREDS

async def setup_livekit_recording(ctx, session_id: str):
    """Set up LiveKit Egress recording for the session."""
    if not LIVEKIT_URL:
        logger.error("LIVEKIT_URL environment variable not set. Cannot start recording.")
        return None, None

    try:
        logger.info(f"Setting up LiveKit Egress recording for session {session_id}")
        recording_path = f"recordings/{session_id}/recording.ogg"
        
        req = api.RoomCompositeEgressRequest(
            room_name=ctx.room.name,
            audio_only=True,
            file_outputs=[api.EncodedFileOutput(
                file_type=api.EncodedFileType.OGG,
                filepath=recording_path,
                gcp=api.GCPUpload(
                    bucket=GCS_BUCKET_NAME,
                    credentials=GOOGLE_JSON_CREDS,
                ),
            )],
        )

        lkapi = api.LiveKitAPI(LIVEKIT_URL)
        res = await lkapi.egress.start_room_composite_egress(req)
        await lkapi.aclose()
        
        logger.info(f"Started LiveKit Egress recording with ID: {res.egress_id}")
        return res.egress_id, recording_path
    except Exception as e:
        logger.error(f"Error setting up LiveKit Egress recording: {e}")
        logger.error(traceback.format_exc())
        return None, None

async def stop_livekit_recording(egress_id: str):
    """Stop the LiveKit Egress recording gracefully."""
    if not egress_id:
        logger.warning("No egress_id provided, cannot stop recording.")
        return False

    if not LIVEKIT_URL:
        logger.error("LIVEKIT_URL environment variable not set. Cannot stop recording.")
        return False

    lkapi = None
    try:
        logger.info(f"Attempting to stop LiveKit Egress recording: {egress_id}")
        lkapi = api.LiveKitAPI(LIVEKIT_URL)
        stop_request = api.StopEgressRequest(egress_id=egress_id)
        await lkapi.egress.stop_egress(stop_request)
        logger.info(f"Successfully sent stop request for Egress recording: {egress_id}")
        return True
    except Exception as e:
        # Gracefully handle cases where the egress is already stopped, completed, or failed.
        # This is expected behavior and not a critical error for the shutdown process.
        error_message = str(e).lower()
        if "cannot be stopped" in error_message or "egress not found" in error_message:
            logger.warning(f"Could not stop egress {egress_id} because it was already in a terminal state (e.g., FAILED, COMPLETE). This is normal. Original error: {e}")
            return True  # Return True because the desired state (stopped) is achieved.
        else:
            logger.error(f"An unexpected error occurred while stopping LiveKit Egress recording: {e}")
            logger.error(traceback.format_exc())
            return False
    finally:
        # Ensure the API client is always closed to prevent resource leaks.
        if lkapi:
            await lkapi.aclose()