from .config import logger

# Base questions for the interview, organized by category.
BASE_QUESTIONS = {
    "personal": [
        "Tell me about yourself and your background.",
        "What are your biggest strengths and weaknesses?",
        "What motivates you to pursue higher education?",
        "What is your biggest achievement so far?",
        "How would your friends describe you?",
        "What challenges have you overcome in your life?",
        "Who is your role model and why?",
        "What makes you special compared to other applicants?",
        "How do you handle failure or setbacks?",
        "What are your long-term career goals?",
        "How have your past experiences prepared you for this scholarship opportunity?",
        "What values do you consider most important in your life?",
        "What drives your passion for your chosen field of study?",
        "Tell me about a time when you had to step out of your comfort zone.",
        "What personal qualities make you a good candidate for LPDP?"
    ],
    "study_plans": [
        "Why did you choose this specific program and university?",
        "Why do you want to study in this country rather than in Indonesia?",
        "How does this program align with your previous education and experience?",
        "How will you ensure you can complete your studies on time?",
        "What specific courses or research areas interest you the most?",
        "Have you contacted any professors at your target university?",
        "What is your research or thesis plan?",
        "How will you adapt to the educational system abroad?",
        "Why not pursue your master's degree at your previous university?",
        "What will you do if you face academic difficulties during your studies?",
        "How did you research your target university and program?",
        "What makes this university's approach to your field unique?",
        "How have you prepared academically for this program?",
        "What do you know about the specific faculty members in your department?",
        "How do you plan to manage your time during your studies?"
    ],
    "contributions": [
        "How will you contribute to Indonesia after completing your studies?",
        "What specific problems in Indonesia do you hope to address with your education?",
        "How will your chosen field benefit Indonesia's development?",
        "What is your 5-year and 10-year plan after graduation?",
        "How will you ensure the knowledge you gain abroad is applicable to Indonesia?",
        "What concrete plans do you have to implement your knowledge in Indonesia?",
        "How will you maintain your commitment to return to Indonesia?",
        "How do you plan to share your knowledge with others in Indonesia?",
        "What impact do you hope to make on your field in Indonesia?",
        "How does your study plan align with Indonesia's national priorities?",
        "What specific institutions or organizations in Indonesia could benefit from your expertise?",
        "How will you adapt international best practices to the Indonesian context?",
        "What networks do you plan to build while studying that could benefit Indonesia?",
        "How will you help bridge international collaboration in your field?",
        "What innovation could you bring to Indonesia after your studies?"
    ],
    "qualifications": [
        "Tell me about your academic achievements.",
        "How did you maintain your GPA while participating in other activities?",
        "Tell me about your research experience or previous thesis.",
        "What makes you academically prepared for this program?",
        "How have you continued developing your knowledge since graduation?",
        "What relevant skills do you have for your chosen field of study?",
        "Have you published any papers or participated in academic conferences?",
        "How will your previous education help you in your future studies?",
        "What academic challenges do you anticipate, and how will you address them?",
        "Why should LPDP invest in your education?",
        "What specific technical or specialized skills do you possess?",
        "How have you demonstrated your ability to succeed in an academic environment?",
        "What relevant professional certifications or training have you completed?",
        "How have you applied theoretical knowledge in practical situations?",
        "What makes you stand out academically from other candidates?"
    ],
    "leadership": [
        "Tell me about your leadership experiences.",
        "What organizations have you been involved with?",
        "How do you define good leadership?",
        "Tell me about a time when you led a team through a difficult situation.",
        "What social activities or community service have you participated in?",
        "How have you contributed to solving problems in your community?",
        "What initiatives have you started or been part of?",
        "How do you handle conflicts within a team?",
        "What is your approach to managing and motivating others?",
        "How have you demonstrated your commitment to serving others?",
        "Describe a situation where you had to make a difficult leadership decision.",
        "How do you balance being authoritative and collaborative as a leader?",
        "Tell me about a time you had to lead people with different backgrounds or perspectives.",
        "What leadership skills do you hope to develop further?",
        "How do you determine the success of your leadership in a project or organization?"
    ],
    "knowledge_indonesia": [
        "What do you think are Indonesia's most pressing challenges right now?",
        "How do you express your love for Indonesia?",
        "What does nationalism mean to you?",
        "How do you view Indonesia's position in the global community?",
        "What areas of infrastructure do we need to develop in Indonesia?",
        "What is your understanding of Pancasila and how do you apply its principles?",
        "What do you know about current economic conditions in Indonesia?",
        "How can your field contribute to Indonesia's sustainable development?",
        "What is your perspective on diversity in Indonesia?",
        "How would you contribute to improving education in Indonesia?",
        "What do you think about Indonesia's digital transformation efforts?",
        "How should Indonesia balance economic development with environmental protection?",
        "What role should Indonesia play in ASEAN and international relations?",
        "How can Indonesia improve its research and innovation capacity?",
        "What strategies could help reduce economic inequality in Indonesia?"
    ]
}

def get_questions_for_interview_type(interview_type: str, category_name: str, sections: list = None) -> dict:
    """Return filtered question sets based on interview type, category, and sections."""
    
    if interview_type == "partial":
        # First, try to filter by provided section IDs
        if sections and isinstance(sections, list) and len(sections) > 0:
            section_ids = [section.get('id') for section in sections if 'id' in section]
            logger.info(f"Filtering questions by section IDs from room config: {section_ids}")
            
            if section_ids:
                filtered_questions = {sid: BASE_QUESTIONS[sid] for sid in section_ids if sid in BASE_QUESTIONS}
                if filtered_questions:
                    logger.info(f"Filtered questions by sections: {list(filtered_questions.keys())}")
                    return filtered_questions
        
        # Fallback to category name mapping if sections aren't usable
        category_mapping = {
            "Personal Background": ["personal"],
            "Study Plans": ["study_plans"], 
            "Future Contributions": ["contributions"],
            "Academic Qualifications": ["qualifications"],
            "Leadership Experience": ["leadership"],
            "Indonesia Knowledge": ["knowledge_indonesia"]
        }
        
        relevant_categories = category_mapping.get(category_name, ["personal"])
        logger.info(f"Filtering questions by category name '{category_name}': {relevant_categories}")
        
        filtered_questions = {cat: BASE_QUESTIONS[cat] for cat in relevant_categories if cat in BASE_QUESTIONS}
        
        # Ensure there's always at least one set of questions
        return filtered_questions or {"personal": BASE_QUESTIONS["personal"]}
    
    # For "full" interviews, return all questions
    logger.info("Full interview type: providing all question categories.")
    return BASE_QUESTIONS

def get_category_focus(category: str, language: str = 'en') -> str:
    """Get focus description for each category in the specified language."""
    focus_map_en = {
        "personal": "personal background, strengths, weaknesses, motivations, achievements, goals, values, challenges overcome",
        "study_plans": "university choice, program selection, academic preparation, research plans, study abroad reasons",
        "contributions": "future contributions to Indonesia, development plans, knowledge application, impact goals",
        "qualifications": "academic achievements, GPA, research experience, skills, certifications, technical abilities", 
        "leadership": "leadership experiences, team management, community service, organizational involvement, conflict resolution",
        "knowledge_indonesia": "Indonesia's challenges, nationalism, Pancasila, economic conditions, infrastructure, diversity"
    }
    focus_map_id = {
        "personal": "latar belakang pribadi, kekuatan, kelemahan, motivasi, pencapaian, tujuan, nilai-nilai, tantangan yang dihadapi",
        "study_plans": "pilihan universitas, seleksi program, persiapan akademik, rencana penelitian, alasan studi ke luar negeri",
        "contributions": "kontribusi masa depan untuk Indonesia, rencana pengembangan, aplikasi pengetahuan, tujuan dampak",
        "qualifications": "pencapaian akademik, IPK, pengalaman penelitian, keterampilan, sertifikasi, kemampuan teknis", 
        "leadership": "pengalaman kepemimpinan, manajemen tim, pelayanan masyarakat, keterlibatan organisasi, resolusi konflik",
        "knowledge_indonesia": "tantangan Indonesia, nasionalisme, Pancasila, kondisi ekonomi, infrastruktur, keberagaman"
    }
    
    if language == 'id':
        return focus_map_id.get(category, "topik wawancara umum")
    return focus_map_en.get(category, "general interview topics")