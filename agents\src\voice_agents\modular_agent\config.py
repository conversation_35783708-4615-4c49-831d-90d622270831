import logging
import os
import sys
import traceback
from dotenv import load_dotenv
import google.generativeai as genai

# Load environment variables from .env file
load_dotenv()

# --- Logging Configuration ---
def setup_logging():
    """Configures the logging for the application."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger("lpdp-interview-agent")

logger = setup_logging()

# --- API Keys and Environment Variables ---
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
LIVEKIT_URL = os.getenv("LIVEKIT_URL")
GOOGLE_JSON_CREDS = os.getenv("GOOGLE_JSON_CREDS")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")
AZURE_OPENAI_LLM_DEPLOYMENT = os.getenv("AZURE_OPENAI_LLM_DEPLOYMENT")
AZURE_OPENAI_STT_DEPLOYMENT = os.getenv("AZURE_OPENAI_STT_DEPLOYMENT")
AZURE_OPENAI_TTS_DEPLOYMENT = os.getenv("AZURE_OPENAI_TTS_DEPLOYMENT")
AZURE_SPEECH_KEY = os.getenv("AZURE_SPEECH_KEY")
AZURE_SPEECH_REGION = os.getenv("AZURE_SPEECH_REGION")
CUSTOM_AZURE_OPENAI_ENDPOINT = os.getenv("CUSTOM_AZURE_OPENAI_ENDPOINT")
CUSTOM_AZURE_OPENAI_API_KEY = os.getenv("CUSTOM_AZURE_OPENAI_API_KEY")

# --- Google AI Configuration ---
if GOOGLE_API_KEY:
    genai.configure(api_key=GOOGLE_API_KEY)
    logger.info("Google AI configured successfully")
else:
    logger.error("GOOGLE_API_KEY not found in environment variables")

# --- Directory Configuration ---
TRANSCRIPTS_DIR = os.path.join(os.getcwd(), "transcripts")
try:
    os.makedirs(TRANSCRIPTS_DIR, exist_ok=True)
    logger.info(f"Transcript directory ensured at: {TRANSCRIPTS_DIR}")
except Exception as e:
    logger.error(f"Failed to create transcript directory: {e}")
    logger.error(traceback.format_exc())

# --- GCS Configuration ---
GCS_BUCKET_NAME = "terang-ai-assets"
GCS_REGION = "asia-southeast2"