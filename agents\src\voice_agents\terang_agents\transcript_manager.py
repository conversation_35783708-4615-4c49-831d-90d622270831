import asyncio
import json
import os
import time
from datetime import datetime
from .configs import logger, TRANSCRIPTS_DIR
from .configs.gcs_utils import upload_transcript_to_gcs
from .helper_functions.utils import detect_question_category_from_content

# Global transcript storage, managed by this module
GLOBAL_TRANSCRIPT = []

class TranscriptManager:
    def __init__(self, session_id: str, room_config: dict, user_metadata: dict):
        self.session_id = session_id
        self.room_config = room_config
        self.user_metadata = user_metadata
        self.transcript_filename = os.path.join(TRANSCRIPTS_DIR, f"{session_id}.json")
        self.text_transcript_filename = os.path.join(TRANSCRIPTS_DIR, f"{session_id}.txt")
        self.last_save_time = 0
        self.egress_id = None
        self.recording_path = None

    def set_recording_details(self, egress_id: str, recording_path: str):
        self.egress_id = egress_id
        self.recording_path = recording_path

    def add_entry(self, role: str, content: str, function_name: str = "unknown"):
        """Adds a new entry to the global transcript."""
        if not content or not content.strip():
            logger.info(f"Skipping empty transcript entry for role {role}")
            return

        entry = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "function": function_name
        }
        GLOBAL_TRANSCRIPT.append(entry)
        logger.info(f"Transcript entry added ({role}). Total entries: {len(GLOBAL_TRANSCRIPT)}")
        # Schedule an immediate save
        asyncio.create_task(self.save_transcript_to_file())

    def load_existing_transcript(self):
        """Loads transcript from a file if it exists for the session."""
        global GLOBAL_TRANSCRIPT
        if os.path.exists(self.transcript_filename):
            try:
                with open(self.transcript_filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    transcript = data.get('transcript', [])
                    GLOBAL_TRANSCRIPT.clear()
                    GLOBAL_TRANSCRIPT.extend(transcript)
                    logger.info(f"Loaded {len(GLOBAL_TRANSCRIPT)} entries from {self.transcript_filename}")
                    return transcript, data.get('session_summary', {})
            except (json.JSONDecodeError, IOError) as e:
                logger.error(f"Error loading existing transcript: {e}")
        return [], {}

    def analyze_session_state(self):
        """Analyzes the current transcript to determine session state, especially for resumes."""
        if not GLOBAL_TRANSCRIPT:
            return {"is_resuming": False, "questions_asked": 0}

        session_resumed_flag = self.room_config.get('session_resumed', False)
        has_meaningful_content = len(GLOBAL_TRANSCRIPT) > 2

        interviewer_entries = [entry for entry in GLOBAL_TRANSCRIPT if entry.get('role') == 'interviewer']
        applicant_entries = [entry for entry in GLOBAL_TRANSCRIPT if entry.get('role') == 'applicant']

        questions_asked = 0
        last_question = None
        last_answer = None

        question_indicators = [
            'why did you choose', 'how will you contribute', 'what are your', 
            'tell me about your', 'how do you', 'what makes you',
            'describe your', 'what is your plan', 'how have you'
        ]

        for entry in interviewer_entries:
            content = entry.get('content', '').lower()
            if any(word in content for word in ['hello', 'welcome', 'ready to begin', 'tell me about yourself']):
                continue
            if any(indicator in content for indicator in question_indicators) or content.endswith('?'):
                questions_asked += 1
                last_question = entry.get('content')

        if applicant_entries:
            last_answer = applicant_entries[-1].get('content')

        last_category = detect_question_category_from_content(last_question) if last_question else None

        return {
            "is_resuming": session_resumed_flag and has_meaningful_content,
            "introduction_completed": any('welcome' in entry.get('content', '').lower() for entry in interviewer_entries),
            "introduction_asked": any('tell me about yourself' in entry.get('content', '').lower() for entry in interviewer_entries),
            "questions_asked": questions_asked,
            "last_category": last_category,
            "last_question": last_question,
            "last_answer": last_answer,
            "total_entries": len(GLOBAL_TRANSCRIPT)
        }

    async def save_transcript_to_file(self, final_save=False):
        """Saves the current transcript to JSON and TXT files."""
        current_time = time.time()
        if not final_save and (current_time - self.last_save_time < 2.0):
            return  # Throttle saves to avoid excessive I/O

        try:
            user_name = self.user_metadata.get('userName', 'unknown')
            user_email = self.user_metadata.get('userEmail', 'unknown')
            
            session_start_time = None
            if GLOBAL_TRANSCRIPT:
                try:
                    session_start_time = datetime.fromisoformat(GLOBAL_TRANSCRIPT[0]['timestamp'])
                except (ValueError, KeyError):
                    pass
            
            elapsed_minutes = (datetime.now() - session_start_time).total_seconds() / 60 if session_start_time else 0

            transcript_data = {
                "session_id": self.session_id,
                "last_updated": datetime.now().isoformat(),
                "room_metadata": self.room_config,
                "user_metadata": self.user_metadata,
                "transcript": GLOBAL_TRANSCRIPT,
                "session_summary": {
                    "user_name": user_name,
                    "user_email": user_email,
                    "interview_id": self.room_config.get('interviewId'),
                    "elapsed_minutes": elapsed_minutes,
                    "total_entries": len(GLOBAL_TRANSCRIPT),
                    "recording_info": {
                        "egress_id": self.egress_id,
                        "path": self.recording_path
                    }
                }
            }

            with open(self.transcript_filename, 'w', encoding='utf-8') as f:
                json.dump(transcript_data, f, ensure_ascii=False, indent=2)

            with open(self.text_transcript_filename, 'w', encoding='utf-8') as f:
                f.write(f"User: {user_name} ({user_email})\n")
                f.write(f"Session ID: {self.session_id}\n\n")
                for entry in GLOBAL_TRANSCRIPT:
                    f.write(f"[{entry.get('role', '').upper()}] ({entry.get('timestamp', '')}):\n{entry.get('content', '')}\n\n")
            
            logger.info(f"Transcript saved for session {self.session_id}. Entries: {len(GLOBAL_TRANSCRIPT)}")
            self.last_save_time = current_time

            # Upload to GCS on each save
            if self.egress_id:
                await upload_transcript_to_gcs(self.session_id, self.transcript_filename)

        except Exception as e:
            logger.error(f"Error saving transcript: {e}")

    async def final_transcript_upload(self):
        """Performs a final save and ensures upload to GCS."""
        logger.info(f"Performing final transcript save and upload for session {self.session_id}")
        await self.save_transcript_to_file(final_save=True)
        # The save function already handles the upload, but we can add a retry here if needed.
        if not await upload_transcript_to_gcs(self.session_id, self.transcript_filename):
            logger.warning("Retrying final transcript upload in 2 seconds...")
            await asyncio.sleep(2)
            await upload_transcript_to_gcs(self.session_id, self.transcript_filename)

def clear_global_transcript():
    """Clears the global transcript list."""
    GLOBAL_TRANSCRIPT.clear()
    logger.info("Global transcript has been cleared.")