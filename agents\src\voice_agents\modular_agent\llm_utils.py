import asyncio
import google.generativeai as genai
from .config import logger, GOOGLE_API_KEY

# Initialize a global model instance for reuse
global_model = None
if GOOGLE_API_KEY:
    try:
        global_model = genai.GenerativeModel(
            'gemini-2.0-flash-001',
            generation_config=genai.types.GenerationConfig(
                temperature=0.7,
                max_output_tokens=4000,
                top_p=0.95,
                top_k=40,
                candidate_count=1,
            )
        )
        logger.info("Global Gemini model initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing global Gemini model: {e}")

async def generate_ai_response(prompt: str, max_retries: int = 2, timeout: float = 8.0) -> str:
    """
    Generate AI response using the global Google Generative AI model.
    Includes optimizations for speed, timeout, and retries.
    """
    if not global_model:
        logger.error("Global model not initialized. Cannot generate AI response.")
        return "AI generation is currently unavailable."

    for attempt in range(max_retries + 1):
        try:
            # Use asyncio.wait_for for a timeout on the generation
            response = await asyncio.wait_for(
                asyncio.to_thread(global_model.generate_content, prompt),
                timeout=timeout
            )
            
            if response and response.text:
                return response.text.strip()
            else:
                logger.warning(f"AI generation returned no text on attempt {attempt + 1}")
                if attempt == max_retries:
                    return "No response was generated by the AI."
                
        except asyncio.TimeoutError:
            logger.warning(f"AI generation timed out on attempt {attempt + 1}")
            if attempt == max_retries:
                return "The AI response took too long and timed out. Using a fallback."
        except Exception as e:
            logger.error(f"Error generating AI response (attempt {attempt + 1}): {e}")
            if attempt == max_retries:
                return "AI generation is temporarily unavailable due to an error."
    
    return "AI generation failed after multiple attempts."