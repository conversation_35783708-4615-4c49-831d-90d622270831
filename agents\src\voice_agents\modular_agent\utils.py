def parse_duration_to_minutes(duration_str: str) -> float:
    """Parse duration string (HH:MM:SS) to minutes."""
    try:
        parts = duration_str.split(':')
        if len(parts) == 3:
            hours, minutes, seconds = map(int, parts)
            return hours * 60 + minutes + seconds / 60
        elif len(parts) == 2:
            minutes, seconds = map(int, parts)
            return minutes + seconds / 60
        else:
            return 60.0  # Default fallback
    except (ValueError, TypeError):
        return 60.0  # Default fallback

def detect_question_category_from_content(question_text: str) -> str | None:
    """Detect category from question content."""
    if not question_text:
        return None
        
    question_lower = question_text.lower()
    
    category_keywords = {
        "personal": ["yourself", "background", "strengths", "weaknesses", "motivates", "achievement"],
        "study_plans": ["program", "university", "study", "research", "thesis", "academic", "courses"],
        "contributions": ["contribute", "indonesia", "development", "plans", "impact", "benefit"],
        "qualifications": ["academic", "achievements", "gpa", "skills", "experience", "research"],
        "leadership": ["leadership", "team", "organizations", "community", "initiative", "conflicts"],
        "knowledge_indonesia": ["indonesia", "challenges", "nationalism", "pancasila", "economic"]
    }
    
    for category, keywords in category_keywords.items():
        if any(keyword in question_lower for keyword in keywords):
            return category
    
    return None

def format_duration_for_speech(time_str: str, language: str = 'en') -> str:
    """Parse the duration into a more speakable format with language support."""
    try:
        hours, minutes, seconds = map(int, time_str.split(':'))
        
        if language == 'id':  # Indonesian
            if hours > 0 and minutes > 0:
                hour_text = f"{hours} jam"
                minute_text = f"{minutes} menit"
                return f"{hour_text} dan {minute_text}"
            elif hours > 0:
                return f"{hours} jam"
            elif minutes > 0:
                return f"{minutes} menit"
            else:
                return "sebentar"  # Fallback for very short durations
        else:  # English (default)
            if hours > 0 and minutes > 0:
                return f"{hours} hour{'s' if hours > 1 else ''} and {minutes} minute{'s' if minutes > 1 else ''}"
            elif hours > 0:
                return f"{hours} hour{'s' if hours > 1 else ''}"
            elif minutes > 0:
                return f"{minutes} minute{'s' if minutes > 1 else ''}"
            else:
                return "a brief moment"  # Fallback for very short durations
    except (ValueError, TypeError):
        return "one hour" if language == 'en' else "satu jam"

def translate_section_names(section_names: list[str], language: str) -> list[str]:
    """Translate section names based on language."""
    if language != 'id':
        return section_names  # Return English names for non-Indonesian
    
    translation_map = {
        "Personal Background": "Latar Belakang Pribadi",
        "Study Plans": "Rencana Studi", 
        "Future Contributions": "Kontribusi Masa Depan",
        "Academic Qualifications": "Kualifikasi Akademik",
        "Leadership Experience": "Pengalaman Kepemimpinan",
        "Indonesia Knowledge": "Pengetahuan Indonesia",
        "personal": "Latar Belakang Pribadi",
        "study_plans": "Rencana Studi",
        "contributions": "Kontribusi Masa Depan", 
        "qualifications": "Kualifikasi Akademik",
        "leadership": "Pengalaman Kepemimpinan",
        "knowledge_indonesia": "Pengetahuan Indonesia"
    }
    
    return [translation_map.get(name, name) for name in section_names]