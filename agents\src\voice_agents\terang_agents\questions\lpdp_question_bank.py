from .configs.config import logger

# Base questions for the interview, organized by category and language.
BASE_QUESTIONS_EN = {
    "personal": [
        "Tell me about yourself and your background.",
        "What are your biggest strengths and weaknesses?",
        "What motivates you to pursue higher education?",
        "What is your biggest achievement so far?",
        "How would your friends describe you?",
        "What challenges have you overcome in your life?",
        "Who is your role model and why?",
        "What makes you special compared to other applicants?",
        "How do you handle failure or setbacks?",
        "What are your long-term career goals?",
        "How have your past experiences prepared you for this scholarship opportunity?",
        "What values do you consider most important in your life?",
        "What drives your passion for your chosen field of study?",
        "Tell me about a time when you had to step out of your comfort zone.",
        "What personal qualities make you a good candidate for LPDP?"
    ],
    "study_plans": [
        "Why did you choose this specific program and university?",
        "Why do you want to study in this country rather than in Indonesia?",
        "How does this program align with your previous education and experience?",
        "How will you ensure you can complete your studies on time?",
        "What specific courses or research areas interest you the most?",
        "Have you contacted any professors at your target university?",
        "What is your research or thesis plan?",
        "How will you adapt to the educational system abroad?",
        "Why not pursue your master's degree at your previous university?",
        "What will you do if you face academic difficulties during your studies?",
        "How did you research your target university and program?",
        "What makes this university's approach to your field unique?",
        "How have you prepared academically for this program?",
        "What do you know about the specific faculty members in your department?",
        "How do you plan to manage your time during your studies?"
    ],
    "contributions": [
        "How will you contribute to Indonesia after completing your studies?",
        "What specific problems in Indonesia do you hope to address with your education?",
        "How will your chosen field benefit Indonesia's development?",
        "What is your 5-year and 10-year plan after graduation?",
        "How will you ensure the knowledge you gain abroad is applicable to Indonesia?",
        "What concrete plans do you have to implement your knowledge in Indonesia?",
        "How will you maintain your commitment to return to Indonesia?",
        "How do you plan to share your knowledge with others in Indonesia?",
        "What impact do you hope to make on your field in Indonesia?",
        "How does your study plan align with Indonesia's national priorities?",
        "What specific institutions or organizations in Indonesia could benefit from your expertise?",
        "How will you adapt international best practices to the Indonesian context?",
        "What networks do you plan to build while studying that could benefit Indonesia?",
        "How will you help bridge international collaboration in your field?",
        "What innovation could you bring to Indonesia after your studies?"
    ],
    "qualifications": [
        "Tell me about your academic achievements.",
        "How did you maintain your GPA while participating in other activities?",
        "Tell me about your research experience or previous thesis.",
        "What makes you academically prepared for this program?",
        "How have you continued developing your knowledge since graduation?",
        "What relevant skills do you have for your chosen field of study?",
        "Have you published any papers or participated in academic conferences?",
        "How will your previous education help you in your future studies?",
        "What academic challenges do you anticipate, and how will you address them?",
        "Why should LPDP invest in your education?",
        "What specific technical or specialized skills do you possess?",
        "How have you demonstrated your ability to succeed in an academic environment?",
        "What relevant professional certifications or training have you completed?",
        "How have you applied theoretical knowledge in practical situations?",
        "What makes you stand out academically from other candidates?"
    ],
    "leadership": [
        "Tell me about your leadership experiences.",
        "What organizations have you been involved with?",
        "How do you define good leadership?",
        "Tell me about a time when you led a team through a difficult situation.",
        "What social activities or community service have you participated in?",
        "How have you contributed to solving problems in your community?",
        "What initiatives have you started or been part of?",
        "How do you handle conflicts within a team?",
        "What is your approach to managing and motivating others?",
        "How have you demonstrated your commitment to serving others?",
        "Describe a situation where you had to make a difficult leadership decision.",
        "How do you balance being authoritative and collaborative as a leader?",
        "Tell me about a time you had to lead people with different backgrounds or perspectives.",
        "What leadership skills do you hope to develop further?",
        "How do you determine the success of your leadership in a project or organization?"
    ],
    "knowledge_indonesia": [
        "What do you think are Indonesia's most pressing challenges right now?",
        "How do you express your love for Indonesia?",
        "What does nationalism mean to you?",
        "How do you view Indonesia's position in the global community?",
        "What areas of infrastructure do we need to develop in Indonesia?",
        "What is your understanding of Pancasila and how do you apply its principles?",
        "What do you know about current economic conditions in Indonesia?",
        "How can your field contribute to Indonesia's sustainable development?",
        "What is your perspective on diversity in Indonesia?",
        "How would you contribute to improving education in Indonesia?",
        "What do you think about Indonesia's digital transformation efforts?",
        "How should Indonesia balance economic development with environmental protection?",
        "What role should Indonesia play in ASEAN and international relations?",
        "How can Indonesia improve its research and innovation capacity?",
        "What strategies could help reduce economic inequality in Indonesia?"
    ]
}

# Indonesian version of the questions
BASE_QUESTIONS_ID = {
    "personal": [
        "Ceritakan tentang diri Anda dan latar belakang Anda.",
        "Apa kekuatan dan kelemahan terbesar Anda?",
        "Apa yang memotivasi Anda untuk mengejar pendidikan tinggi?",
        "Apa pencapaian terbesar Anda sejauh ini?",
        "Bagaimana teman-teman Anda menggambarkan Anda?",
        "Tantangan apa yang telah Anda atasi dalam hidup Anda?",
        "Siapa panutan Anda dan mengapa?",
        "Apa yang membuat Anda istimewa dibandingkan pelamar lain?",
        "Bagaimana Anda menangani kegagalan atau kemunduran?",
        "Apa tujuan karir jangka panjang Anda?",
        "Bagaimana pengalaman masa lalu Anda mempersiapkan Anda untuk kesempatan beasiswa ini?",
        "Nilai-nilai apa yang Anda anggap paling penting dalam hidup Anda?",
        "Apa yang mendorong passion Anda terhadap bidang studi yang Anda pilih?",
        "Ceritakan tentang saat Anda harus keluar dari zona nyaman Anda.",
        "Kualitas pribadi apa yang membuat Anda menjadi kandidat yang baik untuk LPDP?"
    ],
    "study_plans": [
        "Mengapa Anda memilih program dan universitas yang spesifik ini?",
        "Mengapa Anda ingin belajar di negara ini daripada di Indonesia?",
        "Bagaimana program ini sejalan dengan pendidikan dan pengalaman sebelumnya?",
        "Bagaimana Anda akan memastikan dapat menyelesaikan studi tepat waktu?",
        "Mata kuliah atau area penelitian spesifik apa yang paling menarik minat Anda?",
        "Apakah Anda sudah menghubungi profesor di universitas target Anda?",
        "Apa rencana penelitian atau tesis Anda?",
        "Bagaimana Anda akan beradaptasi dengan sistem pendidikan di luar negeri?",
        "Mengapa tidak melanjutkan gelar master di universitas sebelumnya?",
        "Apa yang akan Anda lakukan jika menghadapi kesulitan akademik selama studi?",
        "Bagaimana Anda meneliti universitas dan program target Anda?",
        "Apa yang membuat pendekatan universitas ini terhadap bidang Anda unik?",
        "Bagaimana Anda telah mempersiapkan diri secara akademik untuk program ini?",
        "Apa yang Anda ketahui tentang anggota fakultas spesifik di departemen Anda?",
        "Bagaimana Anda berencana mengatur waktu selama studi?"
    ],
    "contributions": [
        "Bagaimana Anda akan berkontribusi kepada Indonesia setelah menyelesaikan studi?",
        "Masalah spesifik apa di Indonesia yang ingin Anda atasi dengan pendidikan Anda?",
        "Bagaimana bidang yang Anda pilih akan menguntungkan pembangunan Indonesia?",
        "Apa rencana 5 tahun dan 10 tahun Anda setelah lulus?",
        "Bagaimana Anda akan memastikan pengetahuan yang diperoleh di luar negeri dapat diterapkan di Indonesia?",
        "Rencana konkret apa yang Anda miliki untuk mengimplementasikan pengetahuan di Indonesia?",
        "Bagaimana Anda akan mempertahankan komitmen untuk kembali ke Indonesia?",
        "Bagaimana Anda berencana berbagi pengetahuan dengan orang lain di Indonesia?",
        "Dampak apa yang Anda harapkan dapat dibuat pada bidang Anda di Indonesia?",
        "Bagaimana rencana studi Anda sejalan dengan prioritas nasional Indonesia?",
        "Institusi atau organisasi spesifik apa di Indonesia yang bisa mendapat manfaat dari keahlian Anda?",
        "Bagaimana Anda akan mengadaptasi praktik terbaik internasional ke konteks Indonesia?",
        "Jaringan apa yang Anda rencanakan untuk dibangun saat belajar yang dapat menguntungkan Indonesia?",
        "Bagaimana Anda akan membantu menjembatani kolaborasi internasional di bidang Anda?",
        "Inovasi apa yang bisa Anda bawa ke Indonesia setelah studi?"
    ],
    "qualifications": [
        "Ceritakan tentang pencapaian akademik Anda.",
        "Bagaimana Anda mempertahankan IPK sambil berpartisipasi dalam kegiatan lain?",
        "Ceritakan tentang pengalaman penelitian atau tesis sebelumnya.",
        "Apa yang membuat Anda siap secara akademik untuk program ini?",
        "Bagaimana Anda terus mengembangkan pengetahuan sejak lulus?",
        "Keterampilan relevan apa yang Anda miliki untuk bidang studi yang dipilih?",
        "Apakah Anda pernah menerbitkan makalah atau berpartisipasi dalam konferensi akademik?",
        "Bagaimana pendidikan sebelumnya akan membantu Anda dalam studi masa depan?",
        "Tantangan akademik apa yang Anda antisipasi, dan bagaimana Anda akan mengatasinya?",
        "Mengapa LPDP harus berinvestasi dalam pendidikan Anda?",
        "Keterampilan teknis atau khusus spesifik apa yang Anda miliki?",
        "Bagaimana Anda telah menunjukkan kemampuan untuk sukses di lingkungan akademik?",
        "Sertifikasi profesional atau pelatihan relevan apa yang telah Anda selesaikan?",
        "Bagaimana Anda telah menerapkan pengetahuan teoritis dalam situasi praktis?",
        "Apa yang membuat Anda menonjol secara akademik dari kandidat lain?"
    ],
    "leadership": [
        "Ceritakan tentang pengalaman kepemimpinan Anda.",
        "Organisasi apa yang pernah Anda ikuti?",
        "Bagaimana Anda mendefinisikan kepemimpinan yang baik?",
        "Ceritakan tentang saat Anda memimpin tim melalui situasi sulit.",
        "Kegiatan sosial atau pelayanan masyarakat apa yang pernah Anda ikuti?",
        "Bagaimana Anda telah berkontribusi dalam memecahkan masalah di komunitas Anda?",
        "Inisiatif apa yang pernah Anda mulai atau ikuti?",
        "Bagaimana Anda menangani konflik dalam tim?",
        "Apa pendekatan Anda dalam mengelola dan memotivasi orang lain?",
        "Bagaimana Anda telah menunjukkan komitmen untuk melayani orang lain?",
        "Jelaskan situasi di mana Anda harus membuat keputusan kepemimpinan yang sulit.",
        "Bagaimana Anda menyeimbangkan antara bersikap otoritatif dan kolaboratif sebagai pemimpin?",
        "Ceritakan tentang saat Anda harus memimpin orang dengan latar belakang atau perspektif yang berbeda.",
        "Keterampilan kepemimpinan apa yang ingin Anda kembangkan lebih lanjut?",
        "Bagaimana Anda menentukan keberhasilan kepemimpinan Anda dalam proyek atau organisasi?"
    ],
    "knowledge_indonesia": [
        "Menurut Anda, apa tantangan paling mendesak Indonesia saat ini?",
        "Bagaimana Anda mengekspresikan cinta Anda kepada Indonesia?",
        "Apa arti nasionalisme bagi Anda?",
        "Bagaimana Anda melihat posisi Indonesia dalam komunitas global?",
        "Area infrastruktur apa yang perlu kita kembangkan di Indonesia?",
        "Apa pemahaman Anda tentang Pancasila dan bagaimana Anda menerapkan prinsip-prinsipnya?",
        "Apa yang Anda ketahui tentang kondisi ekonomi Indonesia saat ini?",
        "Bagaimana bidang Anda dapat berkontribusi pada pembangunan berkelanjutan Indonesia?",
        "Apa perspektif Anda tentang keberagaman di Indonesia?",
        "Bagaimana Anda akan berkontribusi untuk meningkatkan pendidikan di Indonesia?",
        "Apa pendapat Anda tentang upaya transformasi digital Indonesia?",
        "Bagaimana Indonesia harus menyeimbangkan pembangunan ekonomi dengan perlindungan lingkungan?",
        "Peran apa yang harus dimainkan Indonesia dalam ASEAN dan hubungan internasional?",
        "Bagaimana Indonesia dapat meningkatkan kapasitas penelitian dan inovasinya?",
        "Strategi apa yang dapat membantu mengurangi ketimpangan ekonomi di Indonesia?"
    ]
}

def get_questions_for_interview_type(interview_type: str, category_name: str, sections: list = None, language: str = 'en') -> dict:
    """Return filtered question sets based on interview type, category, and sections."""

    # Select the appropriate question set based on language
    BASE_QUESTIONS = BASE_QUESTIONS_ID if language == 'id' else BASE_QUESTIONS_EN

    if interview_type == "partial":
        # First, try to filter by provided section IDs
        if sections and isinstance(sections, list) and len(sections) > 0:
            section_ids = [section.get('id') for section in sections if 'id' in section]
            logger.info(f"Filtering questions by section IDs from room config: {section_ids}")

            if section_ids:
                filtered_questions = {sid: BASE_QUESTIONS[sid] for sid in section_ids if sid in BASE_QUESTIONS}
                if filtered_questions:
                    logger.info(f"Filtered questions by sections: {list(filtered_questions.keys())}")
                    return filtered_questions

        # Fallback to category name mapping if sections aren't usable
        category_mapping = {
            "Personal Background": ["personal"],
            "Study Plans": ["study_plans"],
            "Future Contributions": ["contributions"],
            "Academic Qualifications": ["qualifications"],
            "Leadership Experience": ["leadership"],
            "Indonesia Knowledge": ["knowledge_indonesia"]
        }

        relevant_categories = category_mapping.get(category_name, ["personal"])
        logger.info(f"Filtering questions by category name '{category_name}': {relevant_categories}")

        filtered_questions = {cat: BASE_QUESTIONS[cat] for cat in relevant_categories if cat in BASE_QUESTIONS}

        # Ensure there's always at least one set of questions
        return filtered_questions or {"personal": BASE_QUESTIONS["personal"]}

    # For "full" interviews, return all questions
    logger.info(f"Full interview type: providing all question categories in {language}.")
    return BASE_QUESTIONS

def get_category_focus(category: str, language: str = 'en') -> str:
    """Get focus description for each category in the specified language."""
    focus_map_en = {
        "personal": "personal background, strengths, weaknesses, motivations, achievements, goals, values, challenges overcome",
        "study_plans": "university choice, program selection, academic preparation, research plans, study abroad reasons",
        "contributions": "future contributions to Indonesia, development plans, knowledge application, impact goals",
        "qualifications": "academic achievements, GPA, research experience, skills, certifications, technical abilities", 
        "leadership": "leadership experiences, team management, community service, organizational involvement, conflict resolution",
        "knowledge_indonesia": "Indonesia's challenges, nationalism, Pancasila, economic conditions, infrastructure, diversity"
    }
    focus_map_id = {
        "personal": "latar belakang pribadi, kekuatan, kelemahan, motivasi, pencapaian, tujuan, nilai-nilai, tantangan yang dihadapi",
        "study_plans": "pilihan universitas, seleksi program, persiapan akademik, rencana penelitian, alasan studi ke luar negeri",
        "contributions": "kontribusi masa depan untuk Indonesia, rencana pengembangan, aplikasi pengetahuan, tujuan dampak",
        "qualifications": "pencapaian akademik, IPK, pengalaman penelitian, keterampilan, sertifikasi, kemampuan teknis", 
        "leadership": "pengalaman kepemimpinan, manajemen tim, pelayanan masyarakat, keterlibatan organisasi, resolusi konflik",
        "knowledge_indonesia": "tantangan Indonesia, nasionalisme, Pancasila, kondisi ekonomi, infrastruktur, keberagaman"
    }
    
    if language == 'id':
        return focus_map_id.get(category, "topik wawancara umum")
    return focus_map_en.get(category, "general interview topics")