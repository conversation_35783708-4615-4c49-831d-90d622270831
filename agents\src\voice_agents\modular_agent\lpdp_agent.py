import asyncio
import json
import random
from datetime import datetime

from livekit.agents import Agent, RunContext
from livekit.agents.llm import function_tool

from .config import logger
from .llm_utils import generate_ai_response
from .lpdp_question_bank import get_questions_for_interview_type, get_category_focus
from .transcript_manager import TranscriptManager, GLOBAL_TRANSCRIPT
from .utils import (
    parse_duration_to_minutes,
    detect_question_category_from_content,
    format_duration_for_speech,
    translate_section_names,
)

class LPDPInterviewAgent(Agent):
    def __init__(self, room_metadata: str, user_metadata: dict, transcript_manager: TranscriptManager):
        self.transcript_manager = transcript_manager
        self.room_config = {}
        if room_metadata:
            try:
                self.room_config = json.loads(room_metadata)
            except json.JSONDecodeError:
                logger.error("Failed to parse room metadata.")
        
        self.user_metadata = user_metadata
        self.language = self.room_config.get('language', 'en')
        
        # Extract resume context like in basic agent
        self.resume_context = self.room_config.get('resume_context', {})
        self.is_existing_session = self.room_config.get('session_resumed', False)
        
        self._prepare_session_state()
        
        # Generate instructions first
        instructions = self._generate_initial_instructions()
        
        # THEN call super().__init__ with the generated instructions
        super().__init__(instructions=instructions)

    def _prepare_session_state(self):
        """Initializes the session state for a new or resumed interview."""
        user_name = self.user_metadata.get('userName', 'Anonymous User')
        first_name = user_name.split()[0] if user_name != "Anonymous User" else user_name

        interview_type = self.room_config.get('interview_type', 'full')
        category_name = self.room_config.get('category_name', 'LPDP')
        sections = self.room_config.get('sections', [])

        self.questions = get_questions_for_interview_type(interview_type, category_name, sections, self.language)

        duration_str = self.room_config.get('duration', '01:00:00')
        duration_minutes = parse_duration_to_minutes(duration_str)

        previous_session_state = self.transcript_manager.analyze_session_state()
        is_resuming = previous_session_state.get("is_resuming", False)

        # Extract asked questions from conversation history
        asked_questions = self._extract_asked_questions_from_transcript()

        self.session_state = {
            "user_name": user_name,
            "first_name": first_name,
            "language": self.language,
            "interview_complete": False,
            "start_time": datetime.now(),
            "duration_minutes": duration_minutes,
            "is_resumed_session": is_resuming,
            "questions_asked": previous_session_state.get("questions_asked", 0),
            "last_category": previous_session_state.get("last_category"),
            "last_answer": previous_session_state.get("last_answer"),
            "introduction_completed": previous_session_state.get("introduction_completed", False),
            "introduction_asked": previous_session_state.get("introduction_asked", False),
            "question_categories": list(self.questions.keys()),
            "available_sections": sections,
            "average_question_time": 4.0,
            "asked_questions": asked_questions,  # Track questions already asked
        }
        logger.info(f"Session state prepared. Resuming: {is_resuming}. Asked questions: {len(asked_questions)}")

    def _extract_asked_questions_from_transcript(self) -> set:
        """Extract questions that have already been asked from the conversation history."""
        asked_questions = set()

        # Get interviewer entries from global transcript
        interviewer_entries = [entry for entry in GLOBAL_TRANSCRIPT if entry.get('role') == 'interviewer']

        for entry in interviewer_entries:
            content = entry.get('content', '').strip()

            # Skip welcome messages and non-question content
            if any(phrase in content.lower() for phrase in [
                'hello', 'welcome', 'ready to begin', 'selamat datang', 'siap untuk memulai',
                'great,', 'baik,', 'thank you', 'terima kasih', 'our time is almost up',
                'waktu kita hampir habis'
            ]):
                continue

            # Check if this is a question (ends with ? or contains question indicators)
            question_indicators = [
                'why did you choose', 'how will you contribute', 'what are your',
                'tell me about your', 'how do you', 'what makes you',
                'describe your', 'what is your plan', 'how have you',
                'mengapa anda memilih', 'bagaimana anda akan berkontribusi', 'apa yang',
                'ceritakan tentang', 'bagaimana anda', 'apa yang membuat anda',
                'jelaskan', 'apa rencana anda', 'bagaimana anda telah'
            ]

            if content.endswith('?') or any(indicator in content.lower() for indicator in question_indicators):
                # Normalize the question for comparison
                normalized_question = self._normalize_question_for_comparison(content)
                asked_questions.add(normalized_question)
                logger.debug(f"Added asked question: {normalized_question[:50]}...")

        logger.info(f"Extracted {len(asked_questions)} previously asked questions from transcript")
        return asked_questions

    def _normalize_question_for_comparison(self, question: str) -> str:
        """Normalize question text for comparison to detect duplicates."""
        # Remove common prefixes and suffixes that might vary
        question = question.strip()

        # Remove contextual prefixes
        prefixes_to_remove = [
            'now let\'s move to our first interview question.',
            'sekarang mari kita lanjut ke pertanyaan wawancara pertama.',
            'building on what you shared,',
            'berdasarkan apa yang anda sampaikan,',
            'given your',
            'mengingat',
            'considering your',
            'mempertimbangkan'
        ]

        question_lower = question.lower()
        for prefix in prefixes_to_remove:
            if prefix in question_lower:
                # Find the position after the prefix and extract the core question
                pos = question_lower.find(prefix)
                if pos != -1:
                    question = question[pos + len(prefix):].strip()
                    break

        # Remove leading/trailing punctuation and normalize spacing
        question = question.strip('.,!?').strip()

        # Normalize multiple spaces to single space
        import re
        question = re.sub(r'\s+', ' ', question)

        return question.lower()

    def _get_unasked_question_from_category(self, category: str) -> str:
        """Get a question from the specified category that hasn't been asked yet."""
        category_questions = self.questions.get(category, [])
        asked_questions = self.session_state.get("asked_questions", set())

        # Find questions that haven't been asked yet
        unasked_questions = []
        for question in category_questions:
            normalized_question = self._normalize_question_for_comparison(question)
            if normalized_question not in asked_questions:
                unasked_questions.append(question)

        # If all questions in this category have been asked, return a random one anyway
        # (this handles edge cases where we run out of unique questions)
        if not unasked_questions:
            logger.warning(f"All questions in category '{category}' have been asked. Reusing questions.")
            unasked_questions = category_questions

        if unasked_questions:
            selected_question = random.choice(unasked_questions)
            # Add this question to the asked questions set
            normalized_selected = self._normalize_question_for_comparison(selected_question)
            self.session_state["asked_questions"].add(normalized_selected)
            logger.info(f"Selected unasked question from category '{category}': {selected_question[:50]}...")
            return selected_question

        # Fallback if no questions available
        fallback_question = "Tell me more about yourself." if self.language == 'en' else "Ceritakan lebih banyak tentang diri Anda."
        return fallback_question

    def _generate_initial_instructions(self) -> str:
        """Generates the detailed initial instruction prompt for the LLM and returns it."""
        state = self.session_state
        first_name = state['first_name']
        interview_name = self.room_config.get('name', 'LPDP Interview')
        duration_str = self.room_config.get('duration', '01:00:00')
        interview_type = self.room_config.get('interview_type', 'full')
        category_name = self.room_config.get('category_name', 'LPDP')
        
        resume_instruction = ""
        if state['is_resumed_session']:
            resume_instruction = f"""
            CRITICAL - SESSION RESUME MODE:
            This is a RESUMED session for {first_name}.
            - NEVER give a welcome message again.
            - NEVER ask for an introduction again.
            - IMMEDIATELY use continue_resume_session() function tool.
            """

        instructions = f"""
        Your name is Terra, an expert LPDP Interviewer.
        
        INTERVIEW CONFIGURATION:
        - Interview Name: {interview_name}
        - Duration: {duration_str}
        - Type: {interview_type}
        - Category: {category_name}
        - Session Type: {"RESUMED SESSION" if state['is_resumed_session'] else "NEW SESSION"}
        - Available Question Categories: {state['question_categories']}
        
        USER INFORMATION:
        - Name: {state['user_name']}
        
        LANGUAGE RULES:
        - The interview will be conducted in {'Bahasa Indonesia' if self.language == 'id' else 'English'}.
        - Always use this language for all your responses.
        - Maintain a professional but friendly tone.

        {resume_instruction}

        MANDATORY FUNCTION TOOL USAGE:
        You MUST use these function tools in the specified order and scenarios.

        NEW SESSION FLOW:
        1. Give welcome message ending with "Ready to begin, {first_name}?"
        2. When user responds → CALL handle_readiness_and_start_interview(user_response)
        3. When user provides full introduction → CALL ask_first_question(previous_answer)
        4. For each subsequent answer → CALL get_next_question(previous_answer)

        RESUMED SESSION FLOW:
        1. Give a brief welcome back message.
        2. IMMEDIATELY CALL continue_resume_session().
        3. For each subsequent answer → CALL get_next_question(previous_answer).

        CRITICAL: Do not use markdown. All returns must be speakable.
        """
        logger.info("Detailed agent instructions generated.")
        return instructions

    async def on_enter(self):
        """Handles the agent's entry into the room."""
        if self.session_state["is_resumed_session"]:
            await self._handle_resume_session()
        else:
            await self._handle_new_session()

    async def _handle_new_session(self):
        """Handles the logic for a new interview session."""
        if not self.session_state["introduction_completed"]:
            user_name = self.session_state['user_name']
            first_name = self.session_state['first_name']
            
            # Get interview details from room config
            interview_name = self.room_config.get('name', 'LPDP Interview')
            category_name = self.room_config.get('category_name', 'General')
            interview_type = self.room_config.get('interview_type', 'Standard')
            duration_str = self.room_config.get('duration', '01:00:00')
            duration = format_duration_for_speech(duration_str, self.language)
            
            # Get sections information for welcome message customization
            sections = self.room_config.get('sections', [])
            section_names = [section.get('name', '') for section in sections if 'name' in section]

            # Translate section names if language is Indonesian
            if section_names:
                section_names = translate_section_names(section_names, self.language)

            # Build additional section info for partial interviews
            section_info = ""
            if section_names and interview_type == "partial":
                if self.language == 'id':
                    section_info = f" dengan fokus pada {', '.join(section_names)}"
                else:
                    section_info = f" focusing on {', '.join(section_names)}"

            # LANGUAGE SPECIFIC welcome messages - COMPREHENSIVE LIKE BASIC AGENT
            if self.language == 'id':
                welcome_message = (
                    f"Halo {user_name}, saya Profesor Terra, dan saya adalah pewawancara LPDP Anda untuk sesi hari ini di Terang AI. "
                    f"Selamat datang di latihan {interview_name} untuk kategori {category_name}{section_info}. "
                    f"Ini adalah sesi wawancara {interview_type} dengan durasi {duration} yang akan membantu Anda mempersiapkan proses seleksi beasiswa LPDP yang sebenarnya. "
                    f"Saya akan menanyakan beberapa pertanyaan relevan selama waktu yang dialokasikan. "
                    f"Mohon jawab dengan jelas dan percaya diri seperti dalam wawancara yang sesungguhnya. "
                    f"Siap untuk memulai, {first_name}?"
                )
            else:
                welcome_message = (
                    f"Hello {user_name}, my name is Professor Terra, and I'm your LPDP interviewer for today's session at Terang AI. "
                    f"Welcome to this {interview_name} practice for the {category_name} category{section_info}. "
                    f"This is a {interview_type} interview session with a duration of {duration} that will help you prepare for the actual LPDP scholarship selection process. "
                    f"I'll be asking you several relevant questions over the allocated time. "
                    f"Please answer clearly and confidently as you would in a real interview. "
                    f"Ready to begin, {first_name}?"
                )
            
            await self.session.say(welcome_message)
            self.transcript_manager.add_entry("interviewer", welcome_message, "on_enter_new")
            self.session_state["introduction_completed"] = True

    async def _handle_resume_session(self):
        """Handles the logic for a resumed interview session."""
        first_name = self.session_state['first_name']
        questions_asked = self.session_state['questions_asked']
        
        # Get elapsed time with robust error handling like in basic agent
        elapsed_time = 0
        if hasattr(self, 'resume_context') and 'elapsed_minutes' in getattr(self, 'resume_context', {}):
            elapsed_time_raw = self.resume_context.get('elapsed_minutes', 0)
            if isinstance(elapsed_time_raw, (int, float)) and elapsed_time_raw > 0:
                elapsed_time = elapsed_time_raw
        
        # Fallback calculation for elapsed time if still zero
        if elapsed_time <= 0:
            # Use a reasonable default based on average time per question
            avg_time_per_question = 4  # 4 minutes per question
            elapsed_time = questions_asked * avg_time_per_question
        
        # Format elapsed time for better speech - LANGUAGE SPECIFIC like basic agent
        if self.language == 'id':
            elapsed_text = f"{int(elapsed_time)} menit" if int(elapsed_time) != 1 else "1 menit"
            welcome_back_message = (
                f"Selamat datang kembali, {first_name}! Saya Profesor Terra, dan kita akan melanjutkan sesi wawancara LPDP Anda. "
                f"Kita telah membahas {questions_asked} pertanyaan dalam {elapsed_text} sebelumnya. "
                f"Mari saya lanjutkan dengan pertanyaan berikutnya."
            )
        else:
            elapsed_text = f"{int(elapsed_time)} minute"
            if int(elapsed_time) != 1:
                elapsed_text += "s"
            welcome_back_message = (
                f"Welcome back, {first_name}! I'm Professor Terra, and we're continuing your LPDP interview session. "
                f"We've covered {questions_asked} questions in our previous {elapsed_text} together. "
                f"Let me continue with our next question."
            )

        await self.session.say(welcome_back_message)
        self.transcript_manager.add_entry("interviewer", welcome_back_message, "on_enter_resumed")
        
        next_question = await self.continue_resume_session(context=None)
        await self.session.say(next_question)

    @function_tool
    async def handle_readiness_and_start_interview(self, context: RunContext, user_response: str):
        """Handle the user's response to 'Ready to begin?' and immediately start the interview."""
        # Only for new sessions
        if self.session_state["is_resumed_session"]:
            return "Session already in progress."
        
        first_name = self.session_state['first_name']
        
        # Combine acknowledgment with introduction question for smoother flow
        if self.language == 'id':
            combined_response = (
                f"Baik, {first_name}! Mari mulai. "
                f"Mari kita awali dengan perkenalan diri Anda, beritahu saya tentang diri Anda dan latar belakang Anda."
            )
        else:
            combined_response = (
                f"Great, {first_name}! Let's begin. "
                f"Let's start with your introduction, please tell me about yourself and your background."
            )
        
        # Mark that we've asked the introduction (but don't count it as a formal interview question yet)
        self.session_state["introduction_asked"] = True
        self.transcript_manager.add_entry("interviewer", combined_response, "handle_readiness")
        
        return combined_response

    @function_tool
    async def ask_first_question(self, context: RunContext, previous_answer: str):
        """Called after the user responds to the introduction to ask the first actual interview question."""
        
        # Only for new sessions
        if self.session_state["is_resumed_session"]:
            return await self.get_next_question(context, "")
        
        first_name = self.session_state['first_name']
        
        # Get the user's introduction from the transcript (look for the most recent applicant entry)
        user_introduction = ""
        # Use GLOBAL_TRANSCRIPT directly since it's imported at the module level
        applicant_entries = [entry for entry in GLOBAL_TRANSCRIPT if entry.get('role') == 'applicant']
        
        if applicant_entries:
            # Get the last applicant entry (which should be their introduction)
            last_entry = applicant_entries[-1]
            user_introduction = last_entry.get('content', '')
            logger.info(f"Found user introduction: {user_introduction[:100]}...")
        else:
            logger.warning("No applicant entries found in transcript")
            user_introduction = ""
        
        # Only proceed if we have a meaningful introduction (more than just "yes" or short responses)
        if len(user_introduction) < 10:
            logger.warning(f"Introduction too short ({len(user_introduction)} chars): '{user_introduction}'")
            # Ask for a proper introduction - LANGUAGE SPECIFIC
            if self.language == 'id':
                request_proper_intro = f"Saya ingin mendengar lebih banyak tentang Anda, {first_name}. Bisakah Anda memberikan perkenalan yang lebih detail tentang latar belakang, pendidikan, dan apa yang membuat Anda tertarik mengajukan beasiswa LPDP?"
            else:
                request_proper_intro = f"I'd like to hear more about you, {first_name}. Could you please give me a more detailed introduction about your background, education, and what brings you to apply for the LPDP scholarship?"
            
            return request_proper_intro
        
        # Generate AI-powered contextual response to their introduction - LANGUAGE SPECIFIC
        if self.language == 'id':
            intro_response_prompt = f"""Sebagai Terra, pewawancara LPDP, berikan respons kontekstual singkat 1 kalimat untuk perkenalan {first_name}:
            
            PERKENALAN: "{user_introduction}"
            
            PERSYARATAN:
            1. Akui sesuatu yang spesifik dari latar belakang mereka
            2. Hubungkan dengan tujuan beasiswa LPDP atau potensi
            3. Terdengar natural dan mendorong
            4. Tepat 1 kalimat dalam Bahasa Indonesia
            
            Contoh format:
            - "Saya bisa melihat latar belakang Anda di [bidang] dan pengalaman dengan [hal spesifik] sejalan dengan misi LPDP untuk mengembangkan pemimpin masa depan, {first_name}."
            - "Perjalanan Anda dari [detail latar belakang] ke [fokus saat ini] menunjukkan jenis pemikiran strategis yang dihargai LPDP pada penerima beasiswa, {first_name}."
            
            Buatlah SATU kalimat kontekstual dalam Bahasa Indonesia yang merespons perkenalan mereka."""
        else:
            intro_response_prompt = f"""As Terra, LPDP interviewer, provide a brief 1-sentence contextual response to {first_name}'s introduction:
            
            INTRODUCTION: "{user_introduction}"
            
            REQUIREMENTS:
            1. Acknowledge something specific from their background
            2. Connect it to LPDP scholarship goals or potential
            3. Sound natural and encouraging
            4. Keep it exactly 1 sentence
            
            Example formats:
            - "I can see your background in [field] and experience with [specific thing] aligns well with LPDP's mission to develop future leaders, {first_name}."
            - "Your journey from [background detail] to [current focus] demonstrates the kind of strategic thinking LPDP values in scholarship recipients, {first_name}."
            
            Generate ONE contextual sentence responding to their introduction."""
        
        try:
            intro_response = await generate_ai_response(intro_response_prompt)
            
            # Clean up the response
            if intro_response.startswith('"') and intro_response.endswith('"'):
                intro_response = intro_response[1:-1]
            
            # Ensure it addresses the user personally if it doesn't already
            if not intro_response.lower().__contains__(first_name.lower()):
                intro_response = intro_response.rstrip('.') + f", {first_name}."
                
        except Exception as e:
            logger.error(f"Error generating contextual intro response: {e}")
            # Fallback response - LANGUAGE SPECIFIC
            if self.language == 'id':
                intro_response = f"Terima kasih atas perkenalan yang komprehensif, {first_name}. Saya bisa melihat Anda memiliki fondasi yang kuat untuk kesempatan beasiswa ini."
            else:
                intro_response = f"Thank you for that comprehensive introduction, {first_name}. I can see you have a strong foundation for this scholarship opportunity."
        
        # Now generate the first contextual interview question
        available_categories = list(self.questions.keys())
        first_category = available_categories[0] if available_categories else "personal"

        # Get an unasked question from the selected category
        base_question = self._get_unasked_question_from_category(first_category)
        
        # Add contextual background to the question - LANGUAGE SPECIFIC
        contextual_question = await self._add_context_to_question(base_question, first_category, first_name, user_introduction)
        
        # Combine the introduction response with the contextual first question - LANGUAGE SPECIFIC
        if self.language == 'id':
            full_response = f"{intro_response}\n\nSekarang mari kita lanjut ke pertanyaan wawancara pertama. {contextual_question}"
        else:
            full_response = f"{intro_response}\n\nNow let's move to our first interview question. {contextual_question}"
        
        # Update session state - now we count this as the first actual interview question
        self.session_state["questions_asked"] = 1
        self.session_state["last_category"] = first_category
        
        logger.info(f"Generated first interview question for {first_name} after proper introduction")
        self.transcript_manager.add_entry("interviewer", full_response, "ask_first_question")
        
        return full_response

    async def _add_context_to_question(self, base_question: str, category: str, first_name: str, previous_answer: str = "") -> str:
        """Add contextual background to predefined questions"""
        
        # LANGUAGE SPECIFIC category contexts
        if self.language == 'id':
            category_contexts = {
                "personal": f"Memahami latar belakang pribadi dan karakter Anda sangat penting untuk evaluasi LPDP, karena beasiswa ini mencari individu dengan fondasi pribadi yang kuat dan kesadaran diri yang jelas. Kualitas pribadi Anda akan menentukan seberapa efektif Anda dapat mewakili Indonesia di luar negeri dan berkontribusi setelah kembali.",
                
                "study_plans": f"LPDP berinvestasi pada pelajar yang telah meneliti perjalanan akademik mereka secara menyeluruh dan dapat menunjukkan pemikiran strategis tentang pendidikan mereka. Rencana studi Anda menunjukkan tingkat komitmen dan persiapan untuk kesuksesan akademik di luar negeri.",
                
                "contributions": f"Tujuan utama beasiswa LPDP adalah mengembangkan pemimpin masa depan yang akan mendorong kemajuan Indonesia. Rencana kontribusi Anda sangat penting untuk mengevaluasi bagaimana pendidikan Anda akan menguntungkan pembangunan bangsa.",
                
                "qualifications": f"LPDP mencari kandidat yang unggul secara akademik yang dapat berhasil menyelesaikan program kelas dunia dan membawa pengetahuan berharga kembali ke Indonesia. Kualifikasi Anda menunjukkan kesiapan untuk tantangan akademik di luar negeri.",
                
                "leadership": f"Kemampuan kepemimpinan adalah kompetensi inti yang dicari LPDP pada penerima beasiswa, karena pelajar masa depan diharapkan mendorong perubahan positif di Indonesia. Pengalaman kepemimpinan Anda menunjukkan potensi untuk membuat dampak yang bermakna.",
                
                "knowledge_indonesia": f"Sebagai pelajar LPDP, Anda akan menjadi duta Indonesia saat belajar di luar negeri dan kontributor kunci untuk pembangunan nasional setelah kembali. Pemahaman Anda tentang tantangan dan peluang Indonesia sangat vital untuk peran ini."
            }
        else:
            category_contexts = {
                "personal": f"Understanding your personal background and character is crucial for LPDP evaluation, as the scholarship seeks individuals with strong personal foundations and clear self-awareness. Your personal qualities will determine how effectively you can represent Indonesia abroad and contribute upon your return.",
                
                "study_plans": f"LPDP invests in scholars who have thoroughly researched their academic journey and can demonstrate strategic thinking about their education. Your study plans reveal your commitment level and preparation for academic success abroad.",
                
                "contributions": f"The ultimate goal of LPDP scholarship is to develop future leaders who will drive Indonesia's progress. Your contribution plans are essential to evaluate how your education will benefit our nation's development.",
                
                "qualifications": f"LPDP seeks academically excellent candidates who can successfully complete world-class programs and bring valuable knowledge back to Indonesia. Your qualifications demonstrate your readiness for academic challenges abroad.",
                
                "leadership": f"Leadership capability is a core competency LPDP looks for in scholarship recipients, as future scholars are expected to drive positive change in Indonesia. Your leadership experiences show your potential to make meaningful impact.",
                
                "knowledge_indonesia": f"As an LPDP scholar, you'll be an ambassador for Indonesia while studying abroad and a key contributor to national development upon return. Your understanding of Indonesia's challenges and opportunities is vital for this role."
            }
        
        context = category_contexts.get(category,
            "Pertanyaan ini membantu kami memahami perspektif dan persiapan Anda untuk program beasiswa LPDP." if self.language == 'id'
            else "This question helps us understand your perspective and preparation for the LPDP scholarship program."
        )
        
        # Add reference to their introduction if meaningful
        connection = ""
        if previous_answer and len(previous_answer) > 50:
            # Generate a brief connection to their introduction - LANGUAGE SPECIFIC
            if self.language == 'id':
                connection_prompt = f"""Berdasarkan perkenalan ini: "{previous_answer}", buatlah frasa penghubung singkat (5-10 kata) yang menghubungkan ke pertanyaan {category}.
                
                Contoh:
                - "Berdasarkan latar belakang teknik Anda,"
                - "Mengingat keterlibatan komunitas Anda,"
                - "Mempertimbangkan perjalanan akademik Anda,"
                
                Buatlah frasa penghubung singkat dalam Bahasa Indonesia:"""
            else:
                connection_prompt = f"""Based on this introduction: "{previous_answer}", create a brief connecting phrase (5-10 words) that links to a {category} question.
                
                Examples:
                - "Building on your engineering background,"
                - "Given your community involvement,"
                - "Considering your academic journey,"
                
                Generate a brief connecting phrase:"""
            
            try:
                connection_phrase = await generate_ai_response(connection_prompt)
                if connection_phrase and len(connection_phrase) < 50:
                    connection = f" {connection_phrase.strip()} "
            except:
                connection = " Berdasarkan apa yang Anda sampaikan, " if self.language == 'id' else " Building on what you shared, "
        
        return f"{context}{connection}{base_question.lower()}"

    @function_tool
    async def get_next_question(self, context: RunContext, previous_answer: str):
        """Generates and asks the next contextual interview question."""
        elapsed_minutes = (datetime.now() - self.session_state["start_time"]).total_seconds() / 60
        remaining_minutes = self.session_state["duration_minutes"] - elapsed_minutes

        if remaining_minutes <= 2:
            self.session_state["interview_complete"] = True
            return "Waktu kita hampir habis. Terima kasih atas partisipasi Anda." if self.language == 'id' else "Our time is almost up. Thank you for your participation."

        # Choose a different category from the last one if possible
        available_categories = [cat for cat in self.session_state['question_categories'] if cat != self.session_state.get('last_category')]
        if not available_categories:
            available_categories = self.session_state['question_categories']

        # Try to get an unasked question from a different category
        selected_category = None
        selected_question = None

        # First, try to find an unasked question from available categories
        for category in available_categories:
            category_questions = self.questions.get(category, [])
            asked_questions = self.session_state.get("asked_questions", set())

            # Check if there are unasked questions in this category
            unasked_in_category = []
            for question in category_questions:
                normalized_question = self._normalize_question_for_comparison(question)
                if normalized_question not in asked_questions:
                    unasked_in_category.append(question)

            if unasked_in_category:
                selected_category = category
                selected_question = random.choice(unasked_in_category)
                # Mark this question as asked
                normalized_selected = self._normalize_question_for_comparison(selected_question)
                self.session_state["asked_questions"].add(normalized_selected)
                break

        # If no unasked questions found, fall back to AI generation
        if not selected_question:
            logger.warning("No unasked questions found in question bank, falling back to AI generation")

            # Get list of asked questions for AI context
            asked_questions_list = list(self.session_state.get("asked_questions", set()))
            asked_questions_context = ", ".join(asked_questions_list[:5]) if asked_questions_list else "none"

            prompt = f"""
            As Terra, an LPDP interviewer, generate the next question for {self.session_state['first_name']}.
            CONTEXT:
            - Previous answer: "{previous_answer}"
            - Last category: {self.session_state.get("last_category", "none")}
            - Available categories: {available_categories}
            - Recently asked questions (avoid similar): {asked_questions_context}
            RULES:
            1. Choose a different category from the last one if possible.
            2. Build on the previous response.
            3. Keep it relevant to LPDP goals.
            4. Avoid asking questions similar to those already asked.
            Language: {self.language}
            Generate ONE clear interview question that hasn't been asked before.
            """

            selected_question = await generate_ai_response(prompt)
            selected_category = detect_question_category_from_content(selected_question) or random.choice(available_categories)

            # Add AI-generated question to asked questions
            normalized_ai_question = self._normalize_question_for_comparison(selected_question)
            self.session_state["asked_questions"].add(normalized_ai_question)

        self.session_state["questions_asked"] += 1
        self.session_state["last_answer"] = previous_answer
        self.session_state["last_category"] = selected_category

        logger.info(f"Selected question from category '{selected_category}': {selected_question[:50]}...")
        self.transcript_manager.add_entry("interviewer", selected_question, "get_next_question")
        return selected_question

    @function_tool
    async def continue_resume_session(self, context: RunContext):
        """MANDATORY function tool for resumed sessions - generates appropriate next question"""
        try:
            first_name = self.session_state['first_name']
            
            # Get context from previous session
            previous_questions = self.session_state.get("questions_asked", 0)
            last_category = self.session_state.get("last_category")
            last_answer = self.session_state.get("last_answer", "")
            
            # FIXED: Get sections from room config - critical for category filtering
            sections = self.session_state.get("available_sections", [])
            section_ids = [section.get('id') for section in sections if 'id' in section]
            
            # FIXED: For partial interviews, strictly enforce section categories
            interview_type = self.room_config.get('interview_type', 'full')
            if interview_type == "partial" and section_ids:
                # Override available_categories with ONLY the categories from section_ids
                available_categories = section_ids
                logger.info(f"RESUME: Strict category enforcement: {section_ids}")
            else:
                # Fall back to all question categories if no section IDs
                available_categories = list(self.questions.keys())
            
            # Choose a different category from the last one if possible
            # BUT ONLY within available categories for partial interviews
            available_categories_filtered = [cat for cat in available_categories if cat != last_category]
            if not available_categories_filtered:
                available_categories_filtered = available_categories
            
            # Try to get an unasked question from available categories first
            selected_category = None
            selected_question = None

            # First, try to find an unasked question from available categories
            for category in available_categories_filtered:
                category_questions = self.questions.get(category, [])
                asked_questions = self.session_state.get("asked_questions", set())

                # Check if there are unasked questions in this category
                unasked_in_category = []
                for question in category_questions:
                    normalized_question = self._normalize_question_for_comparison(question)
                    if normalized_question not in asked_questions:
                        unasked_in_category.append(question)

                if unasked_in_category:
                    selected_category = category
                    selected_question = random.choice(unasked_in_category)
                    # Mark this question as asked
                    normalized_selected = self._normalize_question_for_comparison(selected_question)
                    self.session_state["asked_questions"].add(normalized_selected)
                    break

            # If no unasked questions found, fall back to AI generation
            if not selected_question:
                logger.warning("No unasked questions found for resume session, falling back to AI generation")

                # Get list of asked questions for AI context
                asked_questions_list = list(self.session_state.get("asked_questions", set()))
                asked_questions_context = ", ".join(asked_questions_list[:5]) if asked_questions_list else "none"

                # Generate contextually appropriate question for resume - LANGUAGE SPECIFIC
                if self.language == 'id':
                    resume_prompt = f"""Sebagai Terra, pewawancara LPDP, buatlah pertanyaan selanjutnya untuk sesi yang dilanjutkan dengan {first_name}:

                    KONTEKS:
                    - Ini adalah sesi wawancara yang DILANJUTKAN
                    - Pertanyaan sebelumnya yang ditanyakan: {previous_questions}
                    - Kategori terakhir yang dibahas: {last_category or "tidak diketahui"}
                    - Kategori yang tersedia: {', '.join(available_categories_filtered)}
                    - Respons terakhir tentang: "{last_answer if last_answer else "topik wawancara sebelumnya"}"
                    - Pertanyaan yang sudah ditanyakan (hindari yang serupa): {asked_questions_context}

                    PERSYARATAN:
                    1. Pilih kategori yang berbeda dari {last_category} jika memungkinkan
                    2. Buatlah pertanyaan yang menarik dan relevan dengan LPDP
                    3. Jaga agar tetap percakapan dan profesional
                    4. Jangan menyebut "melanjutkan" - langsung ajukan pertanyaan secara natural
                    5. Hindari pertanyaan yang serupa dengan yang sudah ditanyakan

                    PENTING: Buatlah SATU pertanyaan wawancara yang jelas dalam Bahasa Indonesia."""
                else:
                    resume_prompt = f"""As Terra, LPDP interviewer, generate next question for resumed session with {first_name}:

                    CONTEXT:
                    - This is a RESUMED interview session
                    - Previous questions asked: {previous_questions}
                    - Last category discussed: {last_category or "unknown"}
                    - Available categories: {', '.join(available_categories_filtered)}
                    - Last response was about: "{last_answer if last_answer else "previous interview topics"}"
                    - Recently asked questions (avoid similar): {asked_questions_context}

                    REQUIREMENTS:
                    1. Choose different category from {last_category} if possible
                    2. Generate engaging LPDP-relevant question
                    3. Keep conversational and professional
                    4. Don't mention "continuing" - just ask the question naturally
                    5. Avoid asking questions similar to those already asked

                    Generate ONE clear interview question that hasn't been asked before."""

                try:
                    selected_question = await generate_ai_response(resume_prompt)

                    # Clean up the question
                    if selected_question.startswith('"') and selected_question.endswith('"'):
                        selected_question = selected_question[1:-1]

                    # Detect category
                    selected_category = detect_question_category_from_content(selected_question)

                    # Add AI-generated question to asked questions
                    normalized_ai_question = self._normalize_question_for_comparison(selected_question)
                    self.session_state["asked_questions"].add(normalized_ai_question)

                except Exception as e:
                    logger.error(f"Error generating AI resume question: {e}")
                    # Use fallback from question bank
                    if available_categories_filtered:
                        selected_category = random.choice(available_categories_filtered)
                        selected_question = self._get_unasked_question_from_category(selected_category)
                    else:
                        selected_question = "Tell me about a leadership challenge you've faced." if self.language == 'en' else "Ceritakan tentang tantangan kepemimpinan yang pernah Anda hadapi."
                        selected_category = "leadership"

            # FIXED: Ensure the detected category is within available categories
            if selected_category and selected_category in available_categories:
                self.session_state["last_category"] = selected_category
            elif available_categories:
                # If detected category isn't valid, force to an available category
                self.session_state["last_category"] = available_categories[0]

            # Update questions asked count
            self.session_state["questions_asked"] += 1

            # FIX: Add the continuation question to transcript
            self.transcript_manager.add_entry("interviewer", selected_question, "continue_resume_session")
            logger.info(f"Resume session: Generated continuation question for {first_name} from category '{selected_category}'")

            return selected_question

        except Exception as e:
            logger.error(f"Error in continue_resume_session: {e}")
            first_name = self.session_state.get('first_name', 'there')
            
            # LANGUAGE SPECIFIC fallback
            if self.language == 'id':
                fallback = f"Ceritakan tentang tantangan kepemimpinan yang pernah Anda hadapi dan bagaimana Anda mengatasinya."
            else:
                fallback = f"Tell me about a leadership challenge you've faced and how you overcame it."
            
            # FIX: Add error fallback to transcript
            self.transcript_manager.add_entry("interviewer", fallback, "continue_resume_session_fallback_0")
            return fallback

    @function_tool
    async def analyze_answer_depth(self, context: RunContext, answer: str):
        """Analyze the depth and quality of the user's answer using AI."""
        prompt = f"""Analyze this LPDP candidate's answer briefly in {self.language}: "{answer}". Rate content depth, examples, and relevance on a 1-3 scale. Provide a 1-sentence analysis."""
        return await generate_ai_response(prompt)

    @function_tool
    async def generate_follow_up_question(self, context: RunContext, original_answer: str, topic: str):
        """Generate intelligent follow-up questions to dive deeper into specific topics."""
        prompt = f"""As Terra, create a follow-up question for {self.session_state['first_name']} about {topic}, based on their answer: "{original_answer}". Ask for specific examples. Language: {self.language}."""
        result = await generate_ai_response(prompt)
        return result if result else f"Could you elaborate more on that point, {self.session_state['first_name']}?"